<template>
  <div class="page-container">
    <h1 class="page-title">{{ t('store.title') }}</h1>

    <!-- 搜索区域 -->
    <el-card class="search-card mb-20" shadow="never">
      <el-form :model="searchParams" class="search-form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item :label="t('store.storeCode')">
              <el-input
                v-model="searchParams.storeCode"
                :placeholder="tc('inputPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item :label="t('store.storeName')">
              <el-input
                v-model="searchParams.storeName"
                :placeholder="tc('inputPlaceholder')"
                clearable
                @keyup.enter="handleSearch"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
                          <el-form-item :label="t('store.storeStatus')">
                <el-select
                  v-model="searchParams.storeStatus"
                  :placeholder="tc('pleaseSelect')"
                  clearable
                  style="width: 100%"
                  @change="handleSearch"
                >
                  <el-option :label="tc('all')" value="" />
                  <el-option
                    v-for="option in storeStatusOptions"
                    :key="option.code"
                    :label="option.name"
                    :value="option.code"
                  />
                </el-select>
              </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: right;">
            <el-form-item label="&nbsp;">
              <el-button type="primary" :icon="Search" @click="handleSearch">
                {{ tc('search') }}
              </el-button>
              <el-button :icon="RefreshLeft" @click="handleReset">
                {{ tc('reset') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作区域 & 表格区域 -->
    <el-card class="table-card" shadow="never">
      <div class="table-toolbar mb-20">
        <el-button
          v-permission="'system:store:create'"
          type="primary"
          :icon="Plus"
          @click="handleAdd"
        >
          {{ t('store.addStore') }}
        </el-button>
      </div>

      <el-table
        v-loading="loading"
        :data="tableData"
        stripe
        border
        row-key="id"
        default-expand-all
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
    >
        <el-table-column type="index" :label="tc('index')" width="60" align="center" />
        <el-table-column :label="t('store.storeCode')" prop="storeCode" width="200" />
        <el-table-column :label="t('store.storeName')" prop="storeName" min-width="150" show-overflow-tooltip />
        <el-table-column :label="t('store.storeProperties')" prop="storeProperties" width="150">
          <template #default="{ row }">
            <span>{{ formatStoreProperties(row.storeProperties) }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="t('store.storeType')" prop="storeType" width="120" />
        <el-table-column :label="t('store.manager')" prop="manager" width="120" />
        <el-table-column :label="t('store.contactPhone')" prop="contactPhone" width="150" />
        <el-table-column :label="t('store.storeStatus')" prop="storeStatus" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="row.storeStatus === '00020001' ? 'success' : 'danger'">
              {{ getStatusText(row.storeStatus) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column :label="tc('createTime')" prop="createTime" width="180" />
        <el-table-column :label="tc('operations')" width="240" fixed="right" align="center">
          <template #default="{ row }">
            <el-button
              type="info"
              :icon="View"
              link
              @click="handleView(row)"
            >{{ tc('view') }}</el-button>
            <el-button
              v-permission="'system:store:update'"
              type="primary"
              :icon="Edit"
              link
              @click="handleEdit(row)"
            >{{ tc('edit') }}</el-button>
            <el-button
              v-permission="'system:store:delete'"
              type="danger"
              :icon="Delete"
              link
              @click="handleDelete(row)"
            >{{ tc('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>

       <el-pagination
        v-if="pagination.total > 0"
        class="mt-20"
        :current-page="pagination.current"
        :page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="600px"
      :close-on-click-modal="false"
      destroy-on-close
    >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-position="top"
      class="dialog-form-modern"
    >
    <el-form-item :label="t('store.parentStore')" prop="parentId">
      <el-select v-model="formData.parentId" :placeholder="t('store.selectParentStore')" style="width: 100%" clearable :disabled="isView">
        <el-option
          v-for="store in storeOptions"
          :key="store.id"
          :label="store.storeName"
          :value="store.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('store.storeCode')" prop="storeCode">
      <el-input v-model="formData.storeCode" :placeholder="tc('inputPlaceholder')" :disabled="isEdit || isView"/>
    </el-form-item>
    <el-form-item :label="t('store.storeName')" prop="storeName">
      <el-input v-model="formData.storeName" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.storeType')" prop="storeType">
        <el-select v-model="formData.storeType" :placeholder="t('store.selectStoreType')" style="width: 100%" :disabled="isView">
        <!-- 新增：总部选项 -->
        <el-option :label="t('store.storeTypeHeadquarter')" value="headquarter" />
        <el-option :label="t('store.storeTypeMain')" value="main" />
        <el-option :label="t('store.storeTypeBranch')" value="branch" />
        <el-option :label="t('store.storeTypeWarehouse')" value="warehouse" />
      </el-select>
    </el-form-item>
    <el-form-item :label="t('store.storeProperties')" prop="storeProperties">
      <el-checkbox-group v-model="formData.storeProperties" :disabled="isView">
        <el-checkbox
          v-for="option in storePropertiesOptions"
          :key="option.code"
          :value="option.code"
        >
          {{ option.name }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item :label="t('store.manager')" prop="manager">
      <el-input v-model="formData.manager" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.contactPerson')" prop="contactPerson">
      <el-input v-model="formData.contactPerson" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.contactPhone')" prop="contactPhone">
      <el-input v-model="formData.contactPhone" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.province')" prop="province">
      <el-input v-model="formData.province" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.city')" prop="city">
      <el-input v-model="formData.city" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.district')" prop="district">
      <el-input v-model="formData.district" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.detailAddress')" prop="detailAddress">
      <el-input v-model="formData.detailAddress" :placeholder="tc('inputPlaceholder')" :disabled="isView" />
    </el-form-item>
    <el-form-item :label="t('store.storeStatus')" prop="storeStatus">
      <el-radio-group v-model="formData.storeStatus" :disabled="isView">
        <el-radio value="00020001">{{ t('store.storeStatusNormal') }}</el-radio>
        <el-radio value="00020002">{{ t('store.storeStatusDisabled') }}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item :label="tc('remark')" prop="remark">
      <el-input
        v-model="formData.remark"
        type="textarea"
        :rows="3"
        :placeholder="tc('inputPlaceholder')"
        :disabled="isView"
      />
    </el-form-item>
    </el-form>
      <template #footer>
        <span class="dialog-footer-buttons">
          <el-button @click="dialogVisible = false">{{ isView ? tc('close') : tc('cancel') }}</el-button>
          <el-button v-if="!isView" type="primary" @click="handleSubmit">{{ tc('confirm') }}</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox, ElForm } from 'element-plus';
import { Search, RefreshLeft, Plus, Edit, Delete, View } from '@element-plus/icons-vue';
import { useModuleI18n } from '@/composables/useModuleI18n';
import type { Store, CreateStoreRequest, StoreQueryParams, SelectOption } from '@/types/permission';
import { getStoreList, addStore, updateStore, deleteStore, getStoreDetail, getDictionary } from '@/api/modules/permission';

const { t, tc } = useModuleI18n('base');

const loading = ref(false);
const tableData = ref<Store[]>([]);
const storeOptions = ref<Store[]>([]);
const dialogVisible = ref(false);

const isEdit = ref(false);
const isView = ref(false);
const currentId = ref<string | null>(null);
const storeStatusOptions = ref<SelectOption[]>([]);
const storePropertiesOptions = ref<SelectOption[]>([]);

const formRef = ref<InstanceType<typeof ElForm>>();

const searchParams = reactive<StoreQueryParams>({
  storeName: '',
  storeCode: '',
  storeStatus: '',
});

const pagination = reactive({
  current: 1,
  size: 10,
  total: 0,
});

const getInitialFormData = (): CreateStoreRequest => ({
  storeName: '',
      storeCode: '',
      manager: '',
      contactPerson: '',
      contactPhone: '',
      // 新的地址字段
      province: '',
      city: '',
      district: '',
      detailAddress: '',
      storeType: 'branch',
      storeProperties: [],
      storeStatus: 'active',
      remark: '',
      // 新增 parentId
      parentId: undefined
});

const formData = reactive<CreateStoreRequest>(getInitialFormData());

const dialogTitle = computed(() => {
  if (isView.value) return tc('view') + t('store.title');
  return isEdit.value ? t('store.editStore') : t('store.addStore');
});

const formRules = computed(() => ({
  storeName: [{ required: true, message: t('store.storeNameRequired'), trigger: 'blur' }],
  storeCode: [{ required: true, message: t('store.storeCodeRequired'), trigger: 'blur' }],
  storeType: [{ required: true, message: t('store.storeTypeRequired'), trigger: 'change' }],
}));

const getStatusText = (status: '00020001' | '00020001' | string) => {
  const statusMap = {
    '00020001': t('store.storeStatusNormal'),
    '00020002': t('store.storeStatusDisabled')
  };
  return statusMap[status as keyof typeof statusMap] || status;
};

const formatStoreProperties = (properties?: string[]) => {
  if (!properties || properties.length === 0) return '-';
  const propertyMap = new Map(storePropertiesOptions.value.map(option => [option.code, option.name]));
  return properties.map(p => propertyMap.get(p) || p).join(', ');
};


const buildTree = (stores: Store[]): Store[] => {
  const storeMap = new Map(stores.map(store => [store.id, { ...store, children: [] }]));
  const tree: Store[] = [];

  // 按照您的要求，找到“总部”作为根节点
  const root = stores.find(s => s.storeType === 'headquarter');

  if (root) {
    const rootNode = storeMap.get(root.id)!;
    tree.push(rootNode);

    stores.forEach(store => {
      if (store.id === root.id) return; // 跳过根节点本身

      // 将所有其他门店挂在总部下面
      // 这里简化处理，直接将非总部的门店作为其子节点
      // 如果需要支持多级，则需要判断 parentId
      const childNode = storeMap.get(store.id)!;
      if (store.parentId === root.id || !store.parentId) {
          rootNode.children.push(childNode);
      }
    });

    // 处理多级嵌套的情况
    stores.forEach(store => {
        if(store.parentId && store.parentId !== root.id) {
            const parentNode = storeMap.get(store.parentId);
            if (parentNode) {
                const childNode = storeMap.get(store.id)!;
                // 确保子节点不重复添加
                if (!parentNode.children.some(c => c.id === childNode.id)) {
                    parentNode.children.push(childNode);
                }
            }
        }
    });

  } else {
    // 如果找不到总部，则按原有的 parentId 构建树，作为降级方案
    console.warn("未找到总部类型的根门店。将根据 parentId 构建树形结构。");
    stores.forEach(store => {
      if (store.parentId) {
        const parent = storeMap.get(store.parentId);
        if (parent) {
          parent.children.push(storeMap.get(store.id)!);
        } else {
          tree.push(storeMap.get(store.id)!);
        }
      } else {
        tree.push(storeMap.get(store.id)!);
      }
    });
  }

  return tree;
};

const initData = async () => {
  loading.value = true;
  try {
    const queryParams = {
      ...searchParams,
      current: pagination.current,
      size: pagination.size
    };
    const response = await getStoreList(queryParams);

    if (response.code == 200) {
      const allStores = response.result.records;

      // 检查后端是否已经返回了树形结构数据
      const hasTreeStructure = allStores.some(store => store.children !== undefined);

      if (hasTreeStructure) {
        // 后端已经返回树形结构，直接使用
        tableData.value = allStores;

        // 递归获取所有门店节点（包括子节点）用于父门店选择器
        const flattenStores = (stores: Store[]): Store[] => {
          const result: Store[] = [];
          stores.forEach(store => {
            result.push(store);
            if (store.children && store.children.length > 0) {
              result.push(...flattenStores(store.children));
            }
          });
          return result;
        };
        storeOptions.value = flattenStores(allStores);
      } else {
        // 后端返回平铺数据，需要前端构建树形结构
        storeOptions.value = allStores;
        tableData.value = buildTree(allStores);
      }

      // 使用真实的分页数据
      pagination.total = response.result.total;
      pagination.current = response.result.current;
      pagination.size = response.result.size;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error("加载门店列表失败:", error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.current = 1;
  initData();
};

const handleReset = () => {
  searchParams.storeName = '';
  searchParams.storeCode = '';
  searchParams.storeStatus = '';
  pagination.current = 1;
  initData();
};

const handleAdd = () => {
  isEdit.value = false;
  isView.value = false;
  currentId.value = null;
  Object.assign(formData, getInitialFormData());
  dialogVisible.value = true;
};

const handleEdit = (row: Store) => {
  isEdit.value = true;
  isView.value = false;
  currentId.value = row.id;
  // Make sure all fields from row are copied
  nextTick(() => {
    Object.assign(formData, row);
  });
  dialogVisible.value = true;
};

const handleView = async (row: Store) => {
  try {
    loading.value = true;
    const response = await getStoreDetail(row.id);

    if (response.code == 200) {
      isEdit.value = false;
      isView.value = true;
      currentId.value = row.id;
      Object.assign(formData, response.result);
      dialogVisible.value = true;
    } else {
      ElMessage.error(response.message || tc('loadFailed'));
    }
  } catch (error) {
    console.error('获取门店详情失败:', error);
    ElMessage.error(tc('loadFailed'));
  } finally {
    loading.value = false;
  }
};

const handleDelete = async (row: Store) => {
  try {
    // **已修正**：使用 i18n 的插值功能
    await ElMessageBox.confirm(
      tc('confirmDelete', { item: row.storeName }), // 正确传入要删除的项
      tc('warning'),
      {
        confirmButtonText: tc('confirm'), // 添加中文按钮文字
        cancelButtonText: tc('cancel'),   // 添加中文按钮文字
        type: 'warning'
      }
    );
    await deleteStore(row.id);
    ElMessage.success(tc('success'));
    initData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Delete failed:', error);
    }
  }
};

const handleSubmit = async () => {
  if (!formRef.value) return;
  try {
    await formRef.value.validate();
    loading.value = true;
    if (isEdit.value && currentId.value) {
      const updateData = { ...formData, id: currentId.value };
      await updateStore(updateData);
      ElMessage.success(tc('success'));
    } else {
      await addStore(formData as CreateStoreRequest);
      ElMessage.success(tc('success'));
    }
    dialogVisible.value = false;
    initData();
  } catch (error) {
    console.error('Submit failed:', error);
  } finally {
    loading.value = false;
  }
};

const handleSizeChange = (size: number) => {
  pagination.size = size;
  initData();
};

const handleCurrentChange = (current: number) => {
  pagination.current = current;
  initData();
};

// 加载字典数据
const loadDictionary = async () => {
  try {
    // 加载门店状态字典数据
    const statusResponse = await getDictionary('0002');
    if (statusResponse.code === '200') {
      storeStatusOptions.value = statusResponse.result;
    }

    // 加载门店属性字典数据
    const propertiesResponse = await getDictionary('0200');
    if (propertiesResponse.code === '200') {
      storePropertiesOptions.value = propertiesResponse.result;
    }
  } catch (error) {
    console.error('加载字典数据失败:', error);
  }
};

onMounted(() => {
  initData();
  loadDictionary();
});
</script>

<style lang="scss" scoped>
.page-container {
  padding: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

.search-form {
  .el-form-item {
    margin-bottom: 0; // In a single row form, no need for bottom margin
  }
  .el-form-item__label {
    margin-bottom: 8px;
  }
}

.table-toolbar {
  display: flex;
  gap: 8px;
}

.dialog-form-modern {
  .el-form-item {
    margin-bottom: 22px;
  }
}

.dialog-footer-buttons {
  display: flex;
  justify-content: flex-end;
  .el-button {
    margin-left: 10px;
  }
}
</style>
